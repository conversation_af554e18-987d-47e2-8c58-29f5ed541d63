<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - School Bus Tracking</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            min-height: 100vh;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .header h1 {
            font-size: 1.8rem;
            font-weight: 300;
        }
        
        .header-actions {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .logout-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            transition: background 0.3s ease;
        }
        
        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        .dashboard-container {
            padding: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .action-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .action-card h3 {
            color: #333;
            margin-bottom: 1rem;
            font-size: 1.2rem;
        }
        
        .action-btn {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            transition: transform 0.2s ease;
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
        }
        
        .action-btn:hover {
            transform: translateY(-2px);
        }
        
        .recent-activity {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .recent-activity h3 {
            color: #333;
            margin-bottom: 1rem;
            font-size: 1.2rem;
        }
        
        .activity-list {
            list-style: none;
        }
        
        .activity-item {
            padding: 0.75rem 0;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .activity-item:last-child {
            border-bottom: none;
        }
        
        .activity-text {
            color: #333;
        }
        
        .activity-time {
            color: #666;
            font-size: 0.8rem;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🛠️ Admin Dashboard</h1>
        <div class="header-actions">
            <span>Welcome, {{ admin_username }}!</span>
            <a href="{% url 'admin_logout' %}" class="logout-btn">Logout</a>
        </div>
    </div>

    <div class="dashboard-container">
        <!-- Statistics Cards -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">{{ total_users }}</div>
                <div class="stat-label">Total Users</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ total_students }}</div>
                <div class="stat-label">Students</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ total_drivers }}</div>
                <div class="stat-label">Drivers</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ total_parents }}</div>
                <div class="stat-label">Parents</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ total_routes }}</div>
                <div class="stat-label">Bus Routes</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ online_users }}</div>
                <div class="stat-label">Online Now</div>
            </div>
        </div>

        <!-- Action Cards -->
        <div class="actions-grid">
            <div class="action-card">
                <h3>👥 User Management</h3>
                <p>Manage all system users, roles, and permissions.</p>
                <br>
                <a href="{% url 'manage_users' %}" class="action-btn">Manage Users</a>
            </div>
            
            <div class="action-card">
                <h3>🚌 Route Management</h3>
                <p>Create and manage bus routes, assign drivers and students.</p>
                <br>
                <a href="{% url 'manage_routes' %}" class="action-btn">Manage Routes</a>
            </div>
            
            <div class="action-card">
                <h3>📊 Reports & Analytics</h3>
                <p>View system reports, usage statistics, and analytics.</p>
                <br>
                <a href="#" class="action-btn">View Reports</a>
            </div>
            
            <div class="action-card">
                <h3>⚙️ System Settings</h3>
                <p>Configure system settings and preferences.</p>
                <br>
                <a href="#" class="action-btn">Settings</a>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="recent-activity">
            <h3>📈 Recent Activity</h3>
            <ul class="activity-list">
                {% for user in recent_users %}
                <li class="activity-item">
                    <span class="activity-text">New {{ user.role }} registered: {{ user.username }}</span>
                    <span class="activity-time">{{ user.date_joined|date:"M d, Y" }}</span>
                </li>
                {% endfor %}
                
                {% for route in recent_routes %}
                <li class="activity-item">
                    <span class="activity-text">New route created: {{ route.name }}</span>
                    <span class="activity-time">{{ route.created_at|date:"M d, Y" }}</span>
                </li>
                {% endfor %}
            </ul>
        </div>
    </div>
</body>
</html>
