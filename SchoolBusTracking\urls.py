"""
URL configuration for SchoolBusTracking project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.1/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from authentication import views as auth_views

urlpatterns = [
    #path('location/', include('location.urls')),
    path('barcode/', include('barcode.urls')),
    path('django-admin/', admin.site.urls),  # Renamed to avoid conflict
    path('admin/', include('admin_panel.urls')),  # Our custom admin
    path('', include('authentication.urls')),
    path('accounts/signin', auth_views.signin, name='signin'),
    path('accounts/signup', auth_views.signup, name='signup'),

]
from django.conf import settings
from django.conf.urls.static import static

if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)