<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Dashboard - School Bus Tracking</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            min-height: 100vh;
        }

        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: white;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header h1 {
            font-size: 1.8rem;
            font-weight: 300;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .profile-pic {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }

        .logout-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            transition: background 0.3s ease;
        }

        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .dashboard-container {
            padding: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: 1fr 350px;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .main-content {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            padding: 1.5rem;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .card h3 {
            color: #333;
            margin-bottom: 1rem;
            font-size: 1.2rem;
        }

        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .action-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 1.5rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 10px;
            transition: transform 0.2s ease;
            text-align: center;
        }

        .action-btn:hover {
            transform: translateY(-3px);
        }

        .action-btn .icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .action-btn .label {
            font-weight: 500;
            font-size: 0.9rem;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 0.5rem;
            background: rgba(76, 175, 80, 0.1);
            border-radius: 20px;
            color: #4CAF50;
            font-size: 0.9rem;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #4CAF50;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .notification-item {
            padding: 0.75rem;
            border-left: 4px solid #4facfe;
            background: rgba(79, 172, 254, 0.1);
            border-radius: 0 8px 8px 0;
            margin-bottom: 0.5rem;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .notification-item:hover {
            background: rgba(79, 172, 254, 0.2);
        }

        .notification-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 0.25rem;
        }

        .notification-message {
            font-size: 0.85rem;
            color: #666;
        }

        .attendance-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem;
            border-bottom: 1px solid #eee;
        }

        .attendance-item:last-child {
            border-bottom: none;
        }

        .attendance-date {
            font-weight: 500;
            color: #333;
        }

        .attendance-status {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-present {
            background: #d4edda;
            color: #155724;
        }

        .status-absent {
            background: #f8d7da;
            color: #721c24;
        }

        .status-late {
            background: #fff3cd;
            color: #856404;
        }

        .bus-info {
            text-align: center;
            padding: 1rem;
        }

        .bus-route {
            font-size: 1.1rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .driver-name {
            color: #666;
            margin-bottom: 1rem;
        }

        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }

            .quick-actions {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎓 Student Dashboard</h1>
        <div class="user-info">
            <div class="profile-pic">
                {% if student_profile.profile_picture %}
                    <img src="{{ student_profile.profile_picture.url }}" alt="Profile" style="width: 100%; height: 100%; border-radius: 50%; object-fit: cover;">
                {% else %}
                    👤
                {% endif %}
            </div>
            <div>
                <div>Welcome, {{ user.first_name|default:user.username }}!</div>
                {% if student_location.is_online %}
                    <div class="status-indicator">
                        <div class="status-dot"></div>
                        <span>Online</span>
                    </div>
                {% endif %}
            </div>
            <a href="{% url 'home' %}" class="logout-btn">Logout</a>
        </div>
    </div>

    <div class="dashboard-container">
        <div class="dashboard-grid">
            <div class="main-content">
                <!-- Quick Actions -->
                <div class="card">
                    <h3>🚀 Quick Actions</h3>
                    <div class="quick-actions">
                        <a href="{% url 'student_qr_code' %}" class="action-btn">
                            <div class="icon">📱</div>
                            <div class="label">My QR Code</div>
                        </a>
                        <a href="{% url 'location_tracker' %}" class="action-btn">
                            <div class="icon">📍</div>
                            <div class="label">Track Bus</div>
                        </a>
                        <a href="{% url 'student_profile' %}" class="action-btn">
                            <div class="icon">👤</div>
                            <div class="label">My Profile</div>
                        </a>
                        <a href="{% url 'student_attendance' %}" class="action-btn">
                            <div class="icon">📊</div>
                            <div class="label">Attendance</div>
                        </a>
                    </div>
                </div>

                <!-- Bus Information -->
                {% if bus_route %}
                <div class="card">
                    <h3>🚌 My Bus Information</h3>
                    <div class="bus-info">
                        <div class="bus-route">Route: {{ bus_route.name }}</div>
                        <div class="driver-name">Driver: {{ bus_route.driver.first_name }} {{ bus_route.driver.last_name }}</div>
                        {% if driver_location and driver_location.is_online %}
                            <div class="status-indicator" style="justify-content: center;">
                                <div class="status-dot"></div>
                                <span>Driver is online</span>
                            </div>
                        {% else %}
                            <div style="color: #666;">Driver is offline</div>
                        {% endif %}
                    </div>
                </div>
                {% else %}
                <div class="card">
                    <h3>🚌 Bus Information</h3>
                    <div class="bus-info">
                        <p style="color: #666;">You are not assigned to any bus route yet. Please contact your school administrator.</p>
                    </div>
                </div>
                {% endif %}

                <!-- Recent Attendance -->
                <div class="card">
                    <h3>📅 Recent Attendance</h3>
                    {% if recent_attendance %}
                        {% for attendance in recent_attendance %}
                        <div class="attendance-item">
                            <div class="attendance-date">{{ attendance.date|date:"M d, Y" }}</div>
                            <div class="attendance-status status-{{ attendance.status }}">
                                {{ attendance.get_status_display }}
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <p style="color: #666; text-align: center; padding: 1rem;">No attendance records yet.</p>
                    {% endif %}
                </div>
            </div>

            <div class="sidebar">
                <!-- Notifications -->
                <div class="card">
                    <h3>🔔 Notifications</h3>
                    {% if notifications %}
                        {% for notification in notifications %}
                        <div class="notification-item" onclick="markAsRead({{ notification.id }})">
                            <div class="notification-title">{{ notification.title }}</div>
                            <div class="notification-message">{{ notification.message }}</div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <p style="color: #666; text-align: center; padding: 1rem;">No new notifications</p>
                    {% endif %}
                </div>

                <!-- Student Info -->
                <div class="card">
                    <h3>📋 My Information</h3>
                    <div style="line-height: 1.6;">
                        <div><strong>Student ID:</strong> {{ student_profile.student_id|default:"Not set" }}</div>
                        <div><strong>Grade:</strong> {{ student_profile.grade|default:"Not set" }}</div>
                        <div><strong>School:</strong> {{ student_profile.school|default:"Not set" }}</div>
                        <div><strong>Email:</strong> {{ user.email|default:"Not set" }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function markAsRead(notificationId) {
            fetch(`/api/mark-notification-read/${notificationId}/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    // Remove the notification from view
                    event.target.closest('.notification-item').style.opacity = '0.5';
                }
            });
        }
    </script>
</body>
</html>