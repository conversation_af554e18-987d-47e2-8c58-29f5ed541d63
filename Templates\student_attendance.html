<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Attendance - School Bus Tracking</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            min-height: 100vh;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: white;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .header h1 {
            font-size: 1.8rem;
            font-weight: 300;
        }
        
        .back-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            transition: background 0.3s ease;
        }
        
        .back-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        .container {
            max-width: 1000px;
            margin: 2rem auto;
            padding: 0 2rem;
        }
        
        .attendance-card {
            background: rgba(255, 255, 255, 0.95);
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 10px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        .attendance-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        
        .attendance-table th,
        .attendance-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        .attendance-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }
        
        .attendance-table tr:hover {
            background: #f8f9fa;
        }
        
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .status-present {
            background: #d4edda;
            color: #155724;
        }
        
        .status-absent {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status-late {
            background: #fff3cd;
            color: #856404;
        }
        
        .no-records {
            text-align: center;
            padding: 3rem;
            color: #666;
        }
        
        .no-records .icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }
        
        .filter-section {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 2rem;
        }
        
        .filter-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            align-items: end;
        }
        
        .filter-group {
            display: flex;
            flex-direction: column;
        }
        
        .filter-group label {
            margin-bottom: 0.5rem;
            color: #333;
            font-weight: 500;
        }
        
        .filter-group input,
        .filter-group select {
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        .filter-btn {
            background: #4facfe;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        
        .filter-btn:hover {
            background: #3a8bfe;
        }
        
        @media (max-width: 768px) {
            .attendance-table {
                font-size: 0.9rem;
            }
            
            .attendance-table th,
            .attendance-table td {
                padding: 0.5rem;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📊 My Attendance</h1>
        <a href="{% url 'student_dashboard' %}" class="back-btn">← Back to Dashboard</a>
    </div>

    <div class="container">
        <div class="attendance-card">
            <!-- Statistics -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">{{ attendance_records|length }}</div>
                    <div class="stat-label">Total Days</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">
                        {% with present_count=attendance_records|length %}
                            {% for record in attendance_records %}
                                {% if record.status == 'present' %}{{ forloop.counter0|add:1 }}{% endif %}
                            {% endfor %}
                        {% endwith %}
                    </div>
                    <div class="stat-label">Present</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">
                        {% for record in attendance_records %}
                            {% if record.status == 'absent' %}{{ forloop.counter0|add:1 }}{% endif %}
                        {% endfor %}
                    </div>
                    <div class="stat-label">Absent</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">
                        {% for record in attendance_records %}
                            {% if record.status == 'late' %}{{ forloop.counter0|add:1 }}{% endif %}
                        {% endfor %}
                    </div>
                    <div class="stat-label">Late</div>
                </div>
            </div>

            <!-- Filter Section -->
            <div class="filter-section">
                <form method="get">
                    <div class="filter-grid">
                        <div class="filter-group">
                            <label for="date_from">From Date</label>
                            <input type="date" id="date_from" name="date_from" value="{{ request.GET.date_from }}">
                        </div>
                        <div class="filter-group">
                            <label for="date_to">To Date</label>
                            <input type="date" id="date_to" name="date_to" value="{{ request.GET.date_to }}">
                        </div>
                        <div class="filter-group">
                            <label for="status">Status</label>
                            <select id="status" name="status">
                                <option value="">All Status</option>
                                <option value="present" {% if request.GET.status == 'present' %}selected{% endif %}>Present</option>
                                <option value="absent" {% if request.GET.status == 'absent' %}selected{% endif %}>Absent</option>
                                <option value="late" {% if request.GET.status == 'late' %}selected{% endif %}>Late</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <button type="submit" class="filter-btn">🔍 Filter</button>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Attendance Table -->
            {% if attendance_records %}
                <table class="attendance-table">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Bus Route</th>
                            <th>Boarded Time</th>
                            <th>Alighted Time</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for record in attendance_records %}
                        <tr>
                            <td>{{ record.date|date:"M d, Y" }}</td>
                            <td>{{ record.bus_route.name }}</td>
                            <td>
                                {% if record.boarded_time %}
                                    {{ record.boarded_time|time:"H:i" }}
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                            <td>
                                {% if record.alighted_time %}
                                    {{ record.alighted_time|time:"H:i" }}
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                            <td>
                                <span class="status-badge status-{{ record.status }}">
                                    {{ record.get_status_display }}
                                </span>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            {% else %}
                <div class="no-records">
                    <div class="icon">📅</div>
                    <h3>No Attendance Records</h3>
                    <p>Your attendance records will appear here once you start using the bus service.</p>
                </div>
            {% endif %}
        </div>
    </div>
</body>
</html>
