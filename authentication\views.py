from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib.auth import authenticate, login
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils import timezone
from .forms import SignUpForm
from .models import User, UserLocation, BusRoute, StudentProfile, Attendance, Notification
import json

def home(request):
    return render(request, 'home.html')

@login_required
def student_dashboard(request):
    # Get or create student profile
    student_profile, created = StudentProfile.objects.get_or_create(user=request.user)

    # Get driver location if student is assigned to a route
    driver_location = None
    bus_route = None
    try:
        bus_route = request.user.bus_routes.first()
        if bus_route:
            driver_location = UserLocation.objects.get(user=bus_route.driver)
    except UserLocation.DoesNotExist:
        pass

    # Get recent attendance
    recent_attendance = Attendance.objects.filter(
        student=request.user
    ).order_by('-date')[:5]

    # Get unread notifications
    notifications = Notification.objects.filter(
        user=request.user,
        is_read=False
    )[:5]

    # Get student's current location
    student_location = None
    try:
        student_location = UserLocation.objects.get(user=request.user)
    except UserLocation.DoesNotExist:
        # Create location record if it doesn't exist
        student_location = UserLocation.objects.create(user=request.user)

    context = {
        'student_profile': student_profile,
        'driver_location': driver_location,
        'bus_route': bus_route,
        'recent_attendance': recent_attendance,
        'notifications': notifications,
        'student_location': student_location,
    }
    return render(request, 'student_dashboard.html', context)

@login_required
def driver_dashboard(request):
    # Get driver's routes
    bus_routes = BusRoute.objects.filter(driver=request.user)

    # Get all students assigned to this driver's routes
    students_locations = []
    total_students = 0

    for route in bus_routes:
        total_students += route.students.count()
        for student in route.students.all():
            try:
                location = UserLocation.objects.get(user=student)
                students_locations.append({
                    'user': student,
                    'location': location,
                    'route': route
                })
            except UserLocation.DoesNotExist:
                # Create location if it doesn't exist
                location = UserLocation.objects.create(user=student)
                students_locations.append({
                    'user': student,
                    'location': location,
                    'route': route
                })

    # Get today's attendance for driver's routes
    from datetime import date
    today_attendance = Attendance.objects.filter(
        bus_route__in=bus_routes,
        date=date.today()
    )

    # Get driver's location
    driver_location = None
    try:
        driver_location = UserLocation.objects.get(user=request.user)
    except UserLocation.DoesNotExist:
        driver_location = UserLocation.objects.create(user=request.user)

    context = {
        'bus_routes': bus_routes,
        'students_locations': students_locations,
        'total_students': total_students,
        'today_attendance': today_attendance,
        'driver_location': driver_location,
    }
    return render(request, 'driver_dashboard.html', context)

@login_required
def parent_dashboard(request):
    # Get children locations and driver location
    children_locations = []
    driver_location = None
    
    try:
        children = StudentProfile.objects.filter(parent=request.user)
        for child_profile in children:
            try:
                location = UserLocation.objects.get(user=child_profile.user)
                children_locations.append({
                    'user': child_profile.user,
                    'location': location
                })
                
                # Get driver location for the child's route
                bus_route = child_profile.user.bus_routes.first()
                if bus_route and not driver_location:
                    driver_location = UserLocation.objects.get(user=bus_route.driver)
            except UserLocation.DoesNotExist:
                pass
    except Exception as e:
        pass
    
    context = {
        'children_locations': children_locations,
        'driver_location': driver_location,
    }
    return render(request, 'parent_dashboard.html', context)

@csrf_exempt
@login_required
def update_location(request):
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            latitude = data.get('latitude')
            longitude = data.get('longitude')
            
            if latitude and longitude:
                location, created = UserLocation.objects.get_or_create(user=request.user)
                location.latitude = latitude
                location.longitude = longitude
                location.is_online = True
                location.save()
                
                return JsonResponse({'status': 'success'})
        except Exception as e:
            return JsonResponse({'status': 'error', 'message': str(e)})
    
    return JsonResponse({'status': 'error', 'message': 'Invalid request'})

@login_required
def get_locations(request):
    """API endpoint to get real-time locations based on user role"""
    locations = []
    
    if request.user.role == 'driver':
        # Driver sees all students in their routes
        bus_routes = BusRoute.objects.filter(driver=request.user)
        for route in bus_routes:
            for student in route.students.all():
                try:
                    location = UserLocation.objects.get(user=student, is_online=True)
                    locations.append({
                        'id': student.id,
                        'username': student.username,
                        'first_name': student.first_name,
                        'last_name': student.last_name,
                        'role': student.role,
                        'latitude': float(location.latitude) if location.latitude else None,
                        'longitude': float(location.longitude) if location.longitude else None,
                        'last_updated': location.last_updated.isoformat(),
                    })
                except UserLocation.DoesNotExist:
                    pass
                    
    elif request.user.role == 'student':
        # Student sees their driver
        bus_route = request.user.bus_routes.first()
        if bus_route:
            try:
                location = UserLocation.objects.get(user=bus_route.driver, is_online=True)
                locations.append({
                    'id': bus_route.driver.id,
                    'username': bus_route.driver.username,
                    'first_name': bus_route.driver.first_name,
                    'last_name': bus_route.driver.last_name,
                    'role': bus_route.driver.role,
                    'latitude': float(location.latitude) if location.latitude else None,
                    'longitude': float(location.longitude) if location.longitude else None,
                    'last_updated': location.last_updated.isoformat(),
                })
            except UserLocation.DoesNotExist:
                pass
                
    elif request.user.role == 'parent':
        # Parent sees their children and the driver
        children = StudentProfile.objects.filter(parent=request.user)
        driver_added = False
        
        for child_profile in children:
            # Add child location
            try:
                location = UserLocation.objects.get(user=child_profile.user, is_online=True)
                locations.append({
                    'id': child_profile.user.id,
                    'username': child_profile.user.username,
                    'first_name': child_profile.user.first_name,
                    'last_name': child_profile.user.last_name,
                    'role': child_profile.user.role,
                    'latitude': float(location.latitude) if location.latitude else None,
                    'longitude': float(location.longitude) if location.longitude else None,
                    'last_updated': location.last_updated.isoformat(),
                })
            except UserLocation.DoesNotExist:
                pass
            
            # Add driver location (only once)
            if not driver_added:
                bus_route = child_profile.user.bus_routes.first()
                if bus_route:
                    try:
                        location = UserLocation.objects.get(user=bus_route.driver, is_online=True)
                        locations.append({
                            'id': bus_route.driver.id,
                            'username': bus_route.driver.username,
                            'first_name': bus_route.driver.first_name,
                            'last_name': bus_route.driver.last_name,
                            'role': bus_route.driver.role,
                            'latitude': float(location.latitude) if location.latitude else None,
                            'longitude': float(location.longitude) if location.longitude else None,
                            'last_updated': location.last_updated.isoformat(),
                        })
                        driver_added = True
                    except UserLocation.DoesNotExist:
                        pass
    
    return JsonResponse({'locations': locations})

@csrf_exempt
@login_required
def set_offline(request):
    """Mark user as offline"""
    if request.method == 'POST':
        try:
            location = UserLocation.objects.get(user=request.user)
            location.is_online = False
            location.save()
            return JsonResponse({'status': 'success'})
        except UserLocation.DoesNotExist:
            return JsonResponse({'status': 'error', 'message': 'Location not found'})
    
    return JsonResponse({'status': 'error', 'message': 'Invalid request'})

def signup(request):
    if request.method == 'POST':
        form = SignUpForm(request.POST)
        if form.is_valid():
            user = form.save()
            messages.success(request, 'Account created successfully! You can now log in.')
            # Optionally, log the user in immediately:
            # login(request, user)
            if user.role == 'student':
                return redirect('student_dashboard')
            elif user.role == 'driver':
                return redirect('driver_dashboard')
            elif user.role == 'parent':
                return redirect('parent_dashboard')
            else:
                messages.error(request, 'Invalid role selected')
    else:
        form = SignUpForm()
    return render(request, 'registration/signup.html', {'form': form})

def signin(request):
    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')
        print(username, password)

        user = authenticate(request, username=username, password=password)
        print(user)

        if user is not None:  
            login(request, user)

            if user.role == 'student':
                return redirect('student_dashboard')  # Use the URL name!
            elif user.role == 'driver':
                return redirect('driver_dashboard')
            elif user.role == 'parent':
                return redirect('parent_dashboard')
            else:
                return redirect('home')
        else:
            messages.error(request, 'Invalid username or password')

    return render(request, 'registration/login.html')
def location_tracker(request):
    return render(request, 'location_tracker.html')

@login_required
def student_profile(request):
    """Student profile management"""
    student_profile, created = StudentProfile.objects.get_or_create(user=request.user)

    if request.method == 'POST':
        # Update profile
        student_profile.student_id = request.POST.get('student_id', '')
        student_profile.grade = request.POST.get('grade', '')
        student_profile.school = request.POST.get('school', '')
        student_profile.emergency_contact = request.POST.get('emergency_contact', '')
        student_profile.address = request.POST.get('address', '')

        # Handle profile picture upload
        if 'profile_picture' in request.FILES:
            student_profile.profile_picture = request.FILES['profile_picture']

        student_profile.save()

        # Update user info
        request.user.first_name = request.POST.get('first_name', '')
        request.user.last_name = request.POST.get('last_name', '')
        request.user.email = request.POST.get('email', '')
        request.user.save()

        messages.success(request, 'Profile updated successfully!')
        return redirect('student_profile')

    context = {
        'student_profile': student_profile,
    }
    return render(request, 'student_profile.html', context)

@login_required
def student_qr_code(request):
    """Generate QR code for student"""
    import qrcode
    from io import BytesIO
    import base64

    # Create QR code data
    qr_data = {
        'student_id': request.user.id,
        'username': request.user.username,
        'name': f"{request.user.first_name} {request.user.last_name}",
        'type': 'student_id'
    }

    # Generate QR code
    qr = qrcode.QRCode(version=1, box_size=10, border=5)
    qr.add_data(str(qr_data))
    qr.make(fit=True)

    # Create QR code image
    qr_img = qr.make_image(fill_color="black", back_color="white")

    # Convert to base64 for display
    buffer = BytesIO()
    qr_img.save(buffer, format='PNG')
    qr_code_base64 = base64.b64encode(buffer.getvalue()).decode()

    context = {
        'qr_code_base64': qr_code_base64,
        'qr_data': qr_data,
    }
    return render(request, 'student_qr_code.html', context)

@login_required
def student_attendance(request):
    """View student attendance history"""
    attendance_records = Attendance.objects.filter(
        student=request.user
    ).order_by('-date')

    context = {
        'attendance_records': attendance_records,
    }
    return render(request, 'student_attendance.html', context)

@login_required
def mark_notification_read(request, notification_id):
    """Mark notification as read"""
    try:
        notification = Notification.objects.get(
            id=notification_id,
            user=request.user
        )
        notification.is_read = True
        notification.save()
        return JsonResponse({'status': 'success'})
    except Notification.DoesNotExist:
        return JsonResponse({'status': 'error', 'message': 'Notification not found'})

@login_required
def driver_attendance(request):
    """Driver attendance management"""
    if request.user.role != 'driver':
        return redirect('home')

    bus_routes = BusRoute.objects.filter(driver=request.user)

    # Get today's date
    from datetime import date
    today = date.today()

    # Get or create today's attendance records
    attendance_records = []
    for route in bus_routes:
        for student in route.students.all():
            attendance, created = Attendance.objects.get_or_create(
                student=student,
                bus_route=route,
                date=today,
                defaults={'status': 'absent'}
            )
            attendance_records.append(attendance)

    context = {
        'attendance_records': attendance_records,
        'today': today,
    }
    return render(request, 'driver_attendance.html', context)

@login_required
def mark_attendance(request):
    """Mark student attendance via QR code or manual selection"""
    if request.method == 'POST':
        student_id = request.POST.get('student_id')
        status = request.POST.get('status', 'present')
        action = request.POST.get('action', 'board')  # board or alight

        try:
            student = User.objects.get(id=student_id, role='student')
            bus_route = student.bus_routes.filter(driver=request.user).first()

            if not bus_route:
                return JsonResponse({'status': 'error', 'message': 'Student not in your route'})

            from datetime import date
            attendance, created = Attendance.objects.get_or_create(
                student=student,
                bus_route=bus_route,
                date=date.today(),
                defaults={'status': 'absent'}
            )

            # Update attendance based on action
            if action == 'board':
                attendance.status = status
                attendance.boarded_time = timezone.now()
            elif action == 'alight':
                attendance.alighted_time = timezone.now()

            attendance.save()

            # Create notification for student and parent
            Notification.objects.create(
                user=student,
                title=f"Bus {action.title()}ed",
                message=f"You have {action}ed the bus at {timezone.now().strftime('%H:%M')}",
                notification_type='info'
            )

            # Notify parent if exists
            try:
                student_profile = StudentProfile.objects.get(user=student)
                if student_profile.parent:
                    Notification.objects.create(
                        user=student_profile.parent,
                        title=f"Child {action.title()}ed Bus",
                        message=f"{student.first_name} has {action}ed the bus at {timezone.now().strftime('%H:%M')}",
                        notification_type='info'
                    )
            except StudentProfile.DoesNotExist:
                pass

            return JsonResponse({
                'status': 'success',
                'message': f'Attendance marked for {student.first_name} {student.last_name}'
            })

        except User.DoesNotExist:
            return JsonResponse({'status': 'error', 'message': 'Student not found'})

    return JsonResponse({'status': 'error', 'message': 'Invalid request'})

@login_required
def scan_qr_code(request):
    """QR code scanning interface for drivers"""
    if request.user.role != 'driver':
        return redirect('home')

    return render(request, 'driver_qr_scanner.html')

@login_required
def process_qr_scan(request):
    """Process scanned QR code data"""
    if request.method == 'POST':
        qr_data = request.POST.get('qr_data')
        action = request.POST.get('action', 'board')

        try:
            # Parse QR data (assuming it contains student info)
            import ast
            student_data = ast.literal_eval(qr_data)
            student_id = student_data.get('student_id')

            if not student_id:
                return JsonResponse({'status': 'error', 'message': 'Invalid QR code'})

            # Mark attendance
            return mark_attendance(request)

        except Exception as e:
            return JsonResponse({'status': 'error', 'message': 'Failed to process QR code'})

    return JsonResponse({'status': 'error', 'message': 'Invalid request'})

@login_required
def driver_route_management(request):
    """Driver route management interface"""
    if request.user.role != 'driver':
        return redirect('home')

    bus_routes = BusRoute.objects.filter(driver=request.user)

    context = {
        'bus_routes': bus_routes,
    }
    return render(request, 'driver_routes.html', context)

@login_required
def emergency_alert(request):
    """Send emergency alert to all students and parents"""
    if request.method == 'POST' and request.user.role == 'driver':
        message = request.POST.get('message', 'Emergency alert from your bus driver')

        # Get all students in driver's routes
        bus_routes = BusRoute.objects.filter(driver=request.user)
        students = User.objects.filter(bus_routes__in=bus_routes, role='student').distinct()

        # Send notifications to students
        for student in students:
            Notification.objects.create(
                user=student,
                title='🚨 Emergency Alert',
                message=message,
                notification_type='alert'
            )

            # Also notify parents
            try:
                student_profile = StudentProfile.objects.get(user=student)
                if student_profile.parent:
                    Notification.objects.create(
                        user=student_profile.parent,
                        title='🚨 Emergency Alert - Bus',
                        message=f"Emergency alert for {student.first_name}: {message}",
                        notification_type='alert'
                    )
            except StudentProfile.DoesNotExist:
                pass

        return JsonResponse({
            'status': 'success',
            'message': f'Emergency alert sent to {students.count()} students and their parents'
        })

    return JsonResponse({'status': 'error', 'message': 'Invalid request'})