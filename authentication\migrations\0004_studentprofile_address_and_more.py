# Generated by Django 5.1.1 on 2025-06-24 04:54

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('authentication', '0003_alter_user_role'),
    ]

    operations = [
        migrations.AddField(
            model_name='studentprofile',
            name='address',
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='emergency_contact',
            field=models.CharField(blank=True, max_length=15),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='grade',
            field=models.CharField(blank=True, max_length=10),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='profile_picture',
            field=models.ImageField(blank=True, null=True, upload_to='student_profiles/'),
        ),
        migrations.AddField(
            model_name='studentprofile',
            name='school',
            field=models.Char<PERSON><PERSON>(blank=True, max_length=100),
        ),
        migrations.<PERSON>er<PERSON><PERSON>(
            model_name='studentprofile',
            name='student_id',
            field=models.Char<PERSON>ield(blank=True, max_length=20, null=True, unique=True),
        ),
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('message', models.TextField()),
                ('notification_type', models.CharField(choices=[('info', 'Information'), ('warning', 'Warning'), ('alert', 'Alert'), ('success', 'Success')], default='info', max_length=20)),
                ('is_read', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Attendance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(auto_now_add=True)),
                ('boarded_time', models.DateTimeField(blank=True, null=True)),
                ('alighted_time', models.DateTimeField(blank=True, null=True)),
                ('status', models.CharField(choices=[('present', 'Present'), ('absent', 'Absent'), ('late', 'Late')], default='absent', max_length=20)),
                ('bus_route', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='authentication.busroute')),
                ('student', models.ForeignKey(limit_choices_to={'role': 'student'}, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'unique_together': {('student', 'date')},
            },
        ),
    ]
