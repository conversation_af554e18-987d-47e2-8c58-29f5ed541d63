<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Parent Dashboard</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet/dist/leaflet.css" />
    <style>
        #map {
            height: 80vh;
            width: 100%;
            max-width: 900px;
            margin: 40px auto;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.07);
        }
        body {
            background: #f4f6f8;
            font-family: Arial, sans-serif;
        }
        h2 {
            text-align: center;
            margin-top: 30px;
            color: #2a3cff;
        }
    </style>
</head>
<body>
    <h2>Welcome, Parent!</h2>
    <div id="map"></div>

    <!-- Leaflet JS -->
    <script src="https://unpkg.com/leaflet/dist/leaflet.js"></script>
    <script>
        var map = L.map('map').setView([0, 0], 2);

        <PERSON>.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            maxZoom: 19,
            attribution: '© OpenStreetMap'
        }).addTo(map);

        var markers = {};

        function updateMarkers() {
            fetch("/location/get-locations/")
                .then(response => response.json())
                .then(data => {
                    data.locations.forEach(loc => {
                        if (loc.role === 'driver') {
                            var key = loc.name + '-driver';
                            var icon = L.icon({
                                iconUrl: '/static/icons/driver_icon.png',
                                iconSize: [32, 32]
                            });

                            if (markers[key]) {
                                markers[key].setLatLng([loc.latitude, loc.longitude]);
                            } else {
                                markers[key] = L.marker([loc.latitude, loc.longitude], {icon: icon})
                                    .addTo(map)
                                    .bindPopup("Driver: " + loc.name);
                            }
                        }
                    });
                });
        }

        // Refresh every 5 seconds
        setInterval(updateMarkers, 5000);
        // Initial load
        updateMarkers();
    </script>
</body>
</html>