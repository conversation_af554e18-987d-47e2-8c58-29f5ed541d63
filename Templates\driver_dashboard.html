<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Driver Dashboard - School Bus Tracking</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
            min-height: 100vh;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: white;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .header h1 {
            font-size: 1.8rem;
            font-weight: 300;
        }
        
        .status-indicator {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px 16px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            backdrop-filter: blur(5px);
        }
        
        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #4CAF50;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .dashboard-container {
            padding: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: 350px 1fr;
            gap: 2rem;
            height: calc(100vh - 120px);
        }
        
        .sidebar {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 1.5rem;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow-y: auto;
        }
        
        .sidebar h3 {
            color: #333;
            margin-bottom: 1rem;
            font-size: 1.4rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .student-list {
            max-height: 300px;
            overflow-y: auto;
            margin-bottom: 2rem;
        }
        
        .student-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            margin-bottom: 8px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 10px;
            border-left: 4px solid #4CAF50;
            transition: transform 0.2s;
        }
        
        .student-item:hover {
            transform: translateX(5px);
        }
        
        .student-item.offline {
            border-left-color: #f44336;
            opacity: 0.6;
        }
        
        .student-info {
            flex: 1;
        }
        
        .student-name {
            font-weight: 600;
            color: #333;
        }
        
        .student-status {
            font-size: 0.8rem;
            color: #666;
            margin-top: 2px;
        }
        
        .online-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #4CAF50;
        }
        
        .offline-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #f44336;
        }
        
        .driver-controls {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        
        .control-btn {
            width: 100%;
            padding: 12px;
            margin-bottom: 10px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s;
        }
        
        .start-route-btn {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
        }
        
        .start-route-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
        }
        
        .emergency-btn {
            background: linear-gradient(45deg, #f44336, #d32f2f);
            color: white;
        }
        
        .emergency-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3);
        }
        
        .update-location-btn {
            background: linear-gradient(45deg, #FF9800, #F57C00);
            color: white;
        }
        
        .update-location-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(255, 152, 0, 0.3);
        }
        
        .map-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 1.5rem;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        #map {
            height: calc(100vh - 220px);
            width: 100%;
            border-radius: 10px;
        }
        
        .map-legend {
            display: flex;
            gap: 2rem;
            margin-top: 1rem;
            font-size: 0.9rem;
            color: #666;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .legend-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 2px solid white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        
        .logout-btn {
            background: rgba(244, 67, 54, 0.8);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .logout-btn:hover {
            background: rgba(244, 67, 54, 1);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
            margin-bottom: 1rem;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.9);
            padding: 1rem;
            border-radius: 10px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #FF6B35;
        }
        
        .stat-label {
            font-size: 0.8rem;
            color: #666;
            text-transform: uppercase;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚌 Driver Dashboard</h1>
        <div style="display: flex; align-items: center; gap: 2rem;">
            <div class="status-indicator">
                <div class="status-dot"></div>
                <span>On Duty</span>
            </div>
            <span>Welcome, {{ user.first_name|default:user.username }}!</span>
            <button class="logout-btn" onclick="logout()">Logout</button>
        </div>
    </div>

    <div class="dashboard-container">
        <div class="dashboard-grid">
            <div class="sidebar">
                <h3>📊 Route Statistics</h3>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number" id="total-students">0</div>
                        <div class="stat-label">Total Students</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="online-students">0</div>
                        <div class="stat-label">Online Now</div>
                    </div>
                </div>

                <h3>👥 Students on Route</h3>
                <div class="student-list" id="student-list">
                    <p style="text-align: center; color: #666; padding: 2rem;">Loading students...</p>
                </div>

                <h3>🎛️ Driver Controls</h3>
                <div class="driver-controls">
                    <button class="control-btn start-route-btn" onclick="toggleRoute()">
                        <span id="route-btn-text">Start Route</span>
                    </button>
                    <button class="control-btn update-location-btn" onclick="updateLocation()">
                        📍 Update My Location
                    </button>
                    <button class="control-btn emergency-btn" onclick="emergencyAlert()">
                        🚨 Emergency Alert
                    </button>
                </div>

                <div class="driver-controls">
                    <h4 style="margin-bottom: 10px; color: #333;">My Location</h4>
                    <div id="driver-location" style="font-size: 0.9rem; color: #666;">
                        Getting location...
                    </div>
                </div>
            </div>

            <div class="map-container">
                <h3>🗺️ Real-time Student Tracking</h3>
                <div id="map"></div>
                <div class="map-legend">
                    <div class="legend-item">
                        <div class="legend-dot" style="background: #FF9800;"></div>
                        <span>Bus (You)</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-dot" style="background: #4CAF50;"></div>
                        <span>Students Online</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-dot" style="background: #f44336;"></div>
                        <span>Students Offline</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        let map;
        let driverMarker;
        let studentMarkers = [];
        let driverLocation = null;
        let isOnRoute = false;
        
        // Initialize map
        function initMap() {
            map = L.map('map').setView([0, 0], 13);
            
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors'
            }).addTo(map);
        }
        
        // Get driver's current location
        function getDriverLocation() {
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(
                    function(position) {
                        driverLocation = {
                            lat: position.coords.latitude,
                            lng: position.coords.longitude
                        };
                        
                        updateDriverLocationDisplay();
                        updateLocationOnServer();
                        updateDriverMarker();
                    },
                    function(error) {
                        console.error('Error getting location:', error);
                        document.getElementById('driver-location').innerHTML = 
                            '<p style="color: #f44336;">Unable to get location</p>';
                    }
                );
            }
        }
        
        // Update driver location display
        function updateDriverLocationDisplay() {
            if (driverLocation) {
                document.getElementById('driver-location').innerHTML = `
                    <div><strong>Lat:</strong> ${driverLocation.lat.toFixed(6)}</div>
                    <div><strong>Lng:</strong> ${driverLocation.lng.toFixed(6)}</div>
                    <div style="font-size: 0.8rem; margin-top: 5px;">
                        Updated: ${new Date().toLocaleTimeString()}
                    </div>
                `;
            }
        }
        
        // Update driver marker on map
        function updateDriverMarker() {
            if (driverMarker) map.removeLayer(driverMarker);
            
            if (driverLocation) {
                driverMarker = L.marker([driverLocation.lat, driverLocation.lng], {
                    icon: L.divIcon({
                        className: 'custom-marker',
                        html: '<div style="background: #FF9800; width: 30px; height: 30px; border-radius: 50%; border: 4px solid white; box-shadow: 0 2px 12px rgba(0,0,0,0.4); display: flex; align-items: center; justify-content: center; color: white; font-size: 16px; font-weight: bold;">🚌</div>',
                        iconSize: [30, 30],
                        iconAnchor: [15, 15]
                    })
                }).addTo(map);
                
                driverMarker.bindPopup('Bus Driver (You)');
                map.setView([driverLocation.lat, driverLocation.lng], 13);
            }
        }
        
        // Send location to server
        function updateLocationOnServer() {
            if (driverLocation) {
                fetch('/api/update-location/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCookie('csrftoken')
                    },
                    body: JSON.stringify(driverLocation)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        console.log('Driver location updated successfully');
                    }
                })
                .catch(error => console.error('Error updating location:', error));
            }
        }
        
        // Get all student locations
        function getStudentLocations() {
            fetch('/api/get-locations/')
                .then(response => response.json())
                .then(data => {
                    updateStudentList(data.locations);
                    updateStudentMarkers(data.locations);
                    updateStats(data.locations);
                })
                .catch(error => console.error('Error getting locations:', error));
        }
        
        // Update student list in sidebar
        function updateStudentList(locations) {
            const studentList = document.getElementById('student-list');
            const students = locations.filter(loc => loc.role === 'student');
            
            if (students.length === 0) {
                studentList.innerHTML = '<p style="text-align: center; color: #666; padding: 2rem;">No students assigned to your route</p>';
                return;
            }
            
            studentList.innerHTML = students.map(student => `
                <div class="student-item ${student.latitude ? '' : 'offline'}">
                    <div class="student-info">
                        <div class="student-name">${student.first_name} ${student.last_name}</div>
                        <div class="student-status">
                            ${student.latitude ? 
                                `Last seen: ${new Date(student.last_updated).toLocaleTimeString()}` : 
                                'Offline'
                            }
                        </div>
                    </div>
                    <div class="${student.latitude ? 'online-indicator' : 'offline-indicator'}"></div>
                </div>
            `).join('');
        }
        
        // Update student markers on map
        function updateStudentMarkers(locations) {
            // Clear existing student markers
            studentMarkers.forEach(marker => map.removeLayer(marker));
            studentMarkers = [];
            
            const students = locations.filter(loc => loc.role === 'student' && loc.latitude && loc.longitude);
            
            students.forEach(student => {
                const marker = L.marker([student.latitude, student.longitude], {
                    icon: L.divIcon({
                        className: 'custom-marker',
                        html: '<div style="background: #4CAF50; width: 20px; height: 20px; border-radius: 50%; border: 3px solid white; box-shadow: 0 2px 8px rgba(0,0,0,0.3); display: flex; align-items: center; justify-content: center; color: white; font-size: 10px; font-weight: bold;">👤</div>',
                        iconSize: [20, 20],
                        iconAnchor: [10, 10]
                    })
                }).addTo(map);
                
                marker.bindPopup(`
                    <strong>${student.first_name} ${student.last_name}</strong><br>
                    Student<br>
                    <small>Last updated: ${new Date(student.last_updated).toLocaleString()}</small>
                `);
                
                studentMarkers.push(marker);
            });
        }
        
        // Update statistics
        function updateStats(locations) {
            const students = locations.filter(loc => loc.role === 'student');
            const onlineStudents = students.filter(student => student.latitude && student.longitude);
            
            document.getElementById('total-students').textContent = students.length;
            document.getElementById('online-students').textContent = onlineStudents.length;
        }
        
        // Toggle route status
        function toggleRoute() {
            isOnRoute = !isOnRoute;
            const btn = document.getElementById('route-btn-text');
            const statusDot = document.querySelector('.status-dot');
            const statusText = document.querySelector('.status-indicator span');
            
            if (isOnRoute) {
                btn.textContent = 'End Route';
                statusText.textContent = 'On Route';
                statusDot.style.background = '#FF9800';
            } else {
                btn.textContent = 'Start Route';
                statusText.textContent = 'On Duty';
                statusDot.style.background = '#4CAF50';
            }
        }
        
        // Manual location update
        function updateLocation() {
            getDriverLocation();
        }
        
        // Emergency alert
        function emergencyAlert() {
            if (confirm('Send emergency alert to all students and parents?')) {
                // Here you would implement emergency alert functionality
                alert('Emergency alert sent to all students and parents!');
            }
        }
        
        // Logout function
        function logout() {
            fetch('/api/set-offline/', {
                method: 'POST',
                headers: {
                    'X-CSRFToken': getCookie('csrftoken')
                }
            }).finally(() => {
                window.location.href = '/signin/';
            });
        }
        
        // Get CSRF token
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }
        
        // Initialize everything
        document.addEventListener('DOMContentLoaded', function() {
            initMap();
            getDriverLocation();
            getStudentLocations();
            
            // Update locations every 5 seconds
            setInterval(() => {
                getStudentLocations();
                if (isOnRoute) {
                    getDriverLocation();
                }
            }, 5000);
        });
    </script>
</body>
</html>