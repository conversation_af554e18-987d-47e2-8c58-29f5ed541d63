# Python
__pycache__/
*.py[cod]
*.pyo
*.pyd
*.so

# Django
*.log
db.sqlite3
db.sqlite3*
*.sql
media/
staticfiles/
*.pot
*.pyc

google_vision_key.json
conda-packages.txt

# Virtualenv
env/
venv/
ENV/
.venv/
.Python

# VSCode
.vscode/

# macOS
.DS_Store

# Google OCR credentials
*.json
credentials.json

# Pip
pip-log.txt
pip-delete-this-directory.txt

# Test and coverage
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover

# Migrations
*/migrations/*.pyc
*/migrations/*.pyo
*/migrations/__pycache__/
*/migrations/*.log

# MyPy
.mypy_cache/
.dmypy.json

# Pytest
.pytest_cache/

# Other
.idea/
*.swp
*.swo