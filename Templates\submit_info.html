<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Submit Info</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', sans-serif;
            background-color: #f4f7f8;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
        }

        .form-container {
            background: #ffffff;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            width: 90%;
            max-width: 500px;
        }

        .form-container h1 {
            margin-bottom: 20px;
            text-align: center;
            color: #2c3e50;
        }

        .form-container form {
            display: flex;
            flex-direction: column;
        }

        .form-container form p {
            margin-bottom: 15px;
        }

        .form-container input[type="text"],
        .form-container input[type="email"],
        .form-container input[type="file"],
        .form-container select,
        .form-container textarea {
            width: 100%;
            padding: 10px;
            margin-top: 5px;
            border-radius: 6px;
            border: 1px solid #ccc;
            box-sizing: border-box;
        }

        .form-container button {
            background-color: #3b5998;
            color: white;
            padding: 12px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s ease;
        }

        .form-container button:hover {
            background-color: #2c3e70;
        }

        .error-message {
            color: red;
            margin-bottom: 15px;
            text-align: center;
        }

        @media (max-width: 600px) {
            .form-container {
                padding: 20px;
            }
        }
    </style>
</head>
<body>

    <div class="form-container">
        <h1>Submit Info</h1>

        {% if error %}
          <div class="error-message">{{ error }}</div>
        {% endif %}

        <form method="post" enctype="multipart/form-data">
            {% csrf_token %}
            {{ form.as_p }}
            <button type="submit">Submit</button>
        </form>
    </div>

</body>
</html>