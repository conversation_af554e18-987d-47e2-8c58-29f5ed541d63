<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Sign Up</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            background: #f3f4ef;
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .form-container {
            background: #fff;
            padding: 32px 24px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.07);
            width: 100%;
            max-width: 400px;
        }
        h2 {
            text-align: center;
            margin-bottom: 24px;
            color: #2a3cff;
        }
        .form-group {
            margin-bottom: 18px;
        }
        label {
            display: block;
            margin-bottom: 6px;
            color: #333;
            font-weight: 600;
        }
        input, select {
            width: 100%;
            padding: 10px;
            border-radius: 6px;
            border: 1px solid #ccc;
            font-size: 16px;
        }
        .submit-btn {
            width: 100%;
            padding: 12px;
            background-color: #2a3cff;
            border: none;
            border-radius: 8px;
            color: white;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin-top: 10px;
        }
        .submit-btn:hover {
            background-color: #1a2acc;
        }
        .signin-link {
            margin-top: 18px;
            text-align: center;
            font-size: 14px;
        }
        .signin-link a {
            color: #2a3cff;
            text-decoration: none;
            font-weight: 500;
        }
        .signin-link a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="form-container">
        <h2>Sign Up</h2>
        <form method="post">
            {% csrf_token %}
            {% for field in form %}
                <div class="form-group">
                    {{ field.label_tag }}
                    {{ field }}
                    {% if field.errors %}
                        <div style="color: red;">{{ field.errors|striptags }}</div>
                    {% endif %}
                </div>
            {% endfor %}
            <button type="submit" class="submit-btn">Sign Up</button>
        </form>
        <div class="signin-link">
            Already have an account? <a href="{% url 'signin' %}">Sign In</a>
        </div>
    </div>
</body>
</html>