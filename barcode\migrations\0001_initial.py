# Generated by Django 5.1.1 on 2025-05-20 07:54

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Website',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('receipt_image', models.ImageField(blank=True, null=True, upload_to='receipt_images/')),
                ('picture', models.ImageField(blank=True, null=True, upload_to='user_pictures/')),
                ('qr_code', models.ImageField(blank=True, null=True, upload_to='qr_codes')),
            ],
        ),
    ]
