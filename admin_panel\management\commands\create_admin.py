from django.core.management.base import BaseCommand
from admin_panel.models import AdminUser

class Command(BaseCommand):
    help = 'Create an admin user for the admin panel'

    def add_arguments(self, parser):
        parser.add_argument('--username', type=str, help='Admin username', default='admin')
        parser.add_argument('--email', type=str, help='Admin email', default='<EMAIL>')
        parser.add_argument('--password', type=str, help='Admin password', default='admin123')

    def handle(self, *args, **options):
        username = options['username']
        email = options['email']
        password = options['password']
        
        # Check if admin already exists
        if AdminUser.objects.filter(username=username).exists():
            self.stdout.write(
                self.style.WARNING(f'Admin user "{username}" already exists!')
            )
            return
        
        # Create admin user
        admin_user = AdminUser.objects.create(
            username=username,
            email=email,
            first_name='System',
            last_name='Administrator'
        )
        admin_user.set_password(password)
        admin_user.save()
        
        self.stdout.write(
            self.style.SUCCESS(f'Successfully created admin user "{username}"')
        )
        self.stdout.write(f'Username: {username}')
        self.stdout.write(f'Password: {password}')
        self.stdout.write('You can now login to the admin panel at /admin/login/')
