from django.shortcuts import render, redirect, get_object_or_404
from django.contrib import messages
from django.http import JsonR<PERSON>ponse
from django.views.decorators.csrf import csrf_exempt
from django.utils import timezone
from django.db.models import Count, Q
from authentication.models import User, BusRoute, UserLocation, StudentProfile, DriverProfile
from barcode.models import Website
from .models import AdminUser, SystemSettings
import json

def admin_login(request):
    """Admin login view"""
    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')

        try:
            admin_user = AdminUser.objects.get(username=username, is_active=True)
            if admin_user.check_password(password):
                # Store admin session
                request.session['admin_user_id'] = admin_user.id
                request.session['admin_username'] = admin_user.username
                admin_user.last_login = timezone.now()
                admin_user.save()
                return redirect('admin_dashboard')
            else:
                messages.error(request, 'Invalid credentials')
        except AdminUser.DoesNotExist:
            messages.error(request, 'Invalid credentials')

    return render(request, 'admin_panel/login.html')

def admin_logout(request):
    """Admin logout view"""
    request.session.flush()
    messages.success(request, 'Logged out successfully')
    return redirect('admin_login')

def admin_required(view_func):
    """Decorator to require admin authentication"""
    def wrapper(request, *args, **kwargs):
        if not request.session.get('admin_user_id'):
            return redirect('admin_login')
        return view_func(request, *args, **kwargs)
    return wrapper

@admin_required
def admin_dashboard(request):
    """Main admin dashboard"""
    # Get statistics
    total_users = User.objects.count()
    total_students = User.objects.filter(role='student').count()
    total_drivers = User.objects.filter(role='driver').count()
    total_parents = User.objects.filter(role='parent').count()
    total_routes = BusRoute.objects.count()
    online_users = UserLocation.objects.filter(is_online=True).count()

    # Recent activities
    recent_users = User.objects.order_by('-date_joined')[:5]
    recent_routes = BusRoute.objects.order_by('-created_at')[:5]

    context = {
        'total_users': total_users,
        'total_students': total_students,
        'total_drivers': total_drivers,
        'total_parents': total_parents,
        'total_routes': total_routes,
        'online_users': online_users,
        'recent_users': recent_users,
        'recent_routes': recent_routes,
        'admin_username': request.session.get('admin_username'),
    }

    return render(request, 'admin_panel/dashboard.html', context)

@admin_required
def manage_users(request):
    """Manage all users"""
    users = User.objects.all().order_by('-date_joined')

    if request.method == 'POST':
        action = request.POST.get('action')
        user_id = request.POST.get('user_id')

        if action == 'delete' and user_id:
            try:
                user = User.objects.get(id=user_id)
                user.delete()
                messages.success(request, f'User {user.username} deleted successfully')
            except User.DoesNotExist:
                messages.error(request, 'User not found')

        elif action == 'toggle_active' and user_id:
            try:
                user = User.objects.get(id=user_id)
                user.is_active = not user.is_active
                user.save()
                status = 'activated' if user.is_active else 'deactivated'
                messages.success(request, f'User {user.username} {status} successfully')
            except User.DoesNotExist:
                messages.error(request, 'User not found')

    context = {
        'users': users,
        'admin_username': request.session.get('admin_username'),
    }
    return render(request, 'admin_panel/manage_users.html', context)

@admin_required
def manage_routes(request):
    """Manage bus routes"""
    routes = BusRoute.objects.all().select_related('driver').prefetch_related('students')
    drivers = User.objects.filter(role='driver')
    students = User.objects.filter(role='student')

    if request.method == 'POST':
        action = request.POST.get('action')

        if action == 'create':
            route_name = request.POST.get('route_name')
            driver_id = request.POST.get('driver_id')
            student_ids = request.POST.getlist('student_ids')

            try:
                driver = User.objects.get(id=driver_id, role='driver')
                route = BusRoute.objects.create(name=route_name, driver=driver)

                if student_ids:
                    students_to_add = User.objects.filter(id__in=student_ids, role='student')
                    route.students.set(students_to_add)

                messages.success(request, f'Route {route_name} created successfully')
            except User.DoesNotExist:
                messages.error(request, 'Invalid driver selected')

        elif action == 'delete':
            route_id = request.POST.get('route_id')
            try:
                route = BusRoute.objects.get(id=route_id)
                route.delete()
                messages.success(request, 'Route deleted successfully')
            except BusRoute.DoesNotExist:
                messages.error(request, 'Route not found')

    context = {
        'routes': routes,
        'drivers': drivers,
        'students': students,
        'admin_username': request.session.get('admin_username'),
    }
    return render(request, 'admin_panel/manage_routes.html', context)
