from django.urls import path
from . import views

urlpatterns = [
    path('', views.home, name='home'),
    path('signin/', views.signin, name='signin'),
    path('signup/', views.signup, name='signup'),
    path('student/dashboard/', views.student_dashboard, name='student_dashboard'),
    path('driver/dashboard/', views.driver_dashboard, name='driver_dashboard'),
    path('parent/dashboard/', views.parent_dashboard, name='parent_dashboard'),
    path('location_tracker/', views.location_tracker, name='location_tracker'),

    # Student specific URLs
    path('student/profile/', views.student_profile, name='student_profile'),
    path('student/qr-code/', views.student_qr_code, name='student_qr_code'),
    path('student/attendance/', views.student_attendance, name='student_attendance'),

    # Driver specific URLs
    path('driver/attendance/', views.driver_attendance, name='driver_attendance'),
    path('driver/scan-qr/', views.scan_qr_code, name='scan_qr_code'),
    path('driver/routes/', views.driver_route_management, name='driver_routes'),

    # Location tracking API endpoints
    path('api/update-location/', views.update_location, name='update_location'),
    path('api/get-locations/', views.get_locations, name='get_locations'),
    path('api/set-offline/', views.set_offline, name='set_offline'),
    path('api/mark-notification-read/<int:notification_id>/', views.mark_notification_read, name='mark_notification_read'),

    # Driver API endpoints
    path('api/mark-attendance/', views.mark_attendance, name='mark_attendance'),
    path('api/process-qr-scan/', views.process_qr_scan, name='process_qr_scan'),
    path('api/emergency-alert/', views.emergency_alert, name='emergency_alert'),

]