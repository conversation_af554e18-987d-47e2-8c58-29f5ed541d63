<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My QR Code - School Bus Tracking</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            min-height: 100vh;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: white;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .header h1 {
            font-size: 1.8rem;
            font-weight: 300;
        }
        
        .back-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            transition: background 0.3s ease;
        }
        
        .back-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        .container {
            max-width: 600px;
            margin: 2rem auto;
            padding: 0 2rem;
        }
        
        .qr-card {
            background: rgba(255, 255, 255, 0.95);
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            text-align: center;
        }
        
        .qr-code-container {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            margin: 2rem 0;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        
        .qr-code {
            max-width: 300px;
            width: 100%;
            height: auto;
        }
        
        .student-info {
            background: rgba(79, 172, 254, 0.1);
            padding: 1.5rem;
            border-radius: 10px;
            margin: 1.5rem 0;
            text-align: left;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid rgba(79, 172, 254, 0.2);
        }
        
        .info-item:last-child {
            border-bottom: none;
        }
        
        .info-label {
            font-weight: 600;
            color: #333;
        }
        
        .info-value {
            color: #666;
        }
        
        .instructions {
            background: rgba(255, 193, 7, 0.1);
            padding: 1.5rem;
            border-radius: 10px;
            border-left: 4px solid #ffc107;
            margin: 1.5rem 0;
            text-align: left;
        }
        
        .instructions h3 {
            color: #333;
            margin-bottom: 1rem;
        }
        
        .instructions ul {
            color: #666;
            padding-left: 1.5rem;
        }
        
        .instructions li {
            margin-bottom: 0.5rem;
        }
        
        .action-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin-top: 2rem;
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            transition: transform 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        @media (max-width: 768px) {
            .action-buttons {
                flex-direction: column;
            }
            
            .qr-code-container {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📱 My QR Code</h1>
        <a href="{% url 'student_dashboard' %}" class="back-btn">← Back to Dashboard</a>
    </div>

    <div class="container">
        <div class="qr-card">
            <h2>🎓 Student ID QR Code</h2>
            <p>Use this QR code for bus boarding and attendance tracking</p>
            
            <div class="qr-code-container">
                <img src="data:image/png;base64,{{ qr_code_base64 }}" alt="Student QR Code" class="qr-code">
            </div>
            
            <div class="student-info">
                <div class="info-item">
                    <span class="info-label">Student Name:</span>
                    <span class="info-value">{{ qr_data.name }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Username:</span>
                    <span class="info-value">{{ qr_data.username }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Student ID:</span>
                    <span class="info-value">{{ qr_data.student_id }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">QR Type:</span>
                    <span class="info-value">{{ qr_data.type }}</span>
                </div>
            </div>
            
            <div class="instructions">
                <h3>📋 How to use your QR Code:</h3>
                <ul>
                    <li>Show this QR code to your bus driver when boarding</li>
                    <li>The driver will scan it to mark your attendance</li>
                    <li>Keep this QR code accessible on your phone</li>
                    <li>You can also take a screenshot for offline use</li>
                    <li>Contact your school if you have any issues</li>
                </ul>
            </div>
            
            <div class="action-buttons">
                <button onclick="downloadQR()" class="btn btn-primary">
                    💾 Download QR Code
                </button>
                <button onclick="printQR()" class="btn btn-secondary">
                    🖨️ Print QR Code
                </button>
            </div>
        </div>
    </div>

    <script>
        function downloadQR() {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = document.querySelector('.qr-code');
            
            canvas.width = img.naturalWidth;
            canvas.height = img.naturalHeight;
            
            ctx.drawImage(img, 0, 0);
            
            const link = document.createElement('a');
            link.download = 'student-qr-code.png';
            link.href = canvas.toDataURL();
            link.click();
        }
        
        function printQR() {
            const printWindow = window.open('', '_blank');
            const qrCode = document.querySelector('.qr-code').src;
            const studentName = '{{ qr_data.name }}';
            const studentId = '{{ qr_data.student_id }}';
            
            printWindow.document.write(`
                <html>
                    <head>
                        <title>Student QR Code - ${studentName}</title>
                        <style>
                            body { font-family: Arial, sans-serif; text-align: center; padding: 2rem; }
                            .qr-container { border: 2px solid #333; padding: 2rem; margin: 2rem auto; max-width: 400px; }
                            img { max-width: 300px; }
                            h2 { margin-bottom: 1rem; }
                            p { margin: 0.5rem 0; }
                        </style>
                    </head>
                    <body>
                        <div class="qr-container">
                            <h2>Student QR Code</h2>
                            <img src="${qrCode}" alt="QR Code">
                            <p><strong>Name:</strong> ${studentName}</p>
                            <p><strong>Student ID:</strong> ${studentId}</p>
                            <p><strong>School Bus Tracking System</strong></p>
                        </div>
                    </body>
                </html>
            `);
            printWindow.document.close();
            printWindow.print();
        }
    </script>
</body>
</html>
